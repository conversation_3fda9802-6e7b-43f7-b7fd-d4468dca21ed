import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
// import {
//   NotificationRepository,
//   NotificationResultRepository,
//   NotificationTemplateRepository
// } from '@herond-notification-center/database';
// import { QueueRouterService } from '@herond-notification-center/queue/services/queue-router.service';
// import { NotificationQueueMessage } from '@herond-notification-center/queue/interfaces/queue-message.interface';
// import {
//   NotificationStatus,
//   NotificationChannel,
//   NotificationPriority,
// } from '@herond-notification-center/shared/interfaces/notification.interface';
import { CreateNotificationDto } from '../dto/notification.dto';

// export interface ListNotificationsOptions {
//   skip: number;
//   take: number;
//   status?: NotificationStatus;
//   channel?: string;
// }

@Injectable()
export class NotificationService {
  //   private readonly logger = new Logger(NotificationService.name);
  //   constructor(
  //     private readonly notificationRepository: NotificationRepository,
  //     private readonly notificationResultRepository: NotificationResultRepository,
  //     private readonly notificationTemplateRepository: NotificationTemplateRepository,
  //     private readonly queueRouterService: QueueRouterService
  //   ) {}
  //   /**
  //    * Send a notification by queuing it to appropriate channels
  //    */
  //   async sendNotification(createNotificationDto: CreateNotificationDto) {
  //     this.logger.log('Creating new notification');
  //     try {
  //       // Generate unique notification ID
  //       const notificationId = uuidv4();
  //       // If template is specified, merge with template data
  //       let payload = createNotificationDto.payload;
  //       if (createNotificationDto.templateId) {
  //         const template = await this.notificationTemplateRepository.findById(
  //           createNotificationDto.templateId
  //         );
  //         if (!template) {
  //           throw new NotFoundException(
  //             `Template with ID ${createNotificationDto.templateId} not found`
  //           );
  //         }
  //         // Merge template payload with provided payload
  //         payload = {
  //           ...template.payload,
  //           ...createNotificationDto.payload,
  //         };
  //       }
  //       // Validate channels are supported
  //       for (const channel of createNotificationDto.channels) {
  //         if (!this.queueRouterService.isChannelSupported(channel)) {
  //           throw new BadRequestException(`Unsupported notification channel: ${channel}`);
  //         }
  //       }
  //       // Create notification record
  //       const notification = await this.notificationRepository.create({
  //         id: notificationId,
  //         templateId: createNotificationDto.templateId,
  //         payload,
  //         recipients: createNotificationDto.recipients,
  //         channels: createNotificationDto.channels,
  //         priority: createNotificationDto.priority || NotificationPriority.NORMAL,
  //         status: NotificationStatus.QUEUED,
  //         scheduledAt: createNotificationDto.scheduledAt,
  //         metadata: createNotificationDto.metadata || {},
  //         createdBy: createNotificationDto.createdBy || 'system',
  //         createdAt: new Date(),
  //       });
  //       // Create queue message
  //       const queueMessage: NotificationQueueMessage = {
  //         id: notificationId,
  //         notification: {
  //           id: notificationId,
  //           templateId: createNotificationDto.templateId,
  //           payload,
  //           recipients: createNotificationDto.recipients,
  //           channels: createNotificationDto.channels,
  //           priority: createNotificationDto.priority || NotificationPriority.NORMAL,
  //           scheduledAt: createNotificationDto.scheduledAt,
  //           metadata: createNotificationDto.metadata || {},
  //           createdBy: createNotificationDto.createdBy || 'system',
  //         },
  //         retryCount: 0,
  //         maxRetries: 3,
  //         createdAt: new Date(),
  //         scheduledAt: createNotificationDto.scheduledAt,
  //       };
  //       // Route to appropriate queues
  //       if (createNotificationDto.scheduledAt && createNotificationDto.scheduledAt > new Date()) {
  //         // Schedule for later (implement scheduling logic)
  //         this.logger.log(
  //           `Notification ${notificationId} scheduled for ${createNotificationDto.scheduledAt}`
  //         );
  //         // TODO: Implement scheduling mechanism
  //       } else {
  //         // Send immediately
  //         await this.queueRouterService.routeNotification(queueMessage);
  //       }
  //       this.logger.log(`Notification ${notificationId} queued successfully`);
  //       return notification;
  //     } catch (error) {
  //       this.logger.error(`Failed to send notification: ${error.message}`);
  //       throw error;
  //     }
  //   }
  //   /**
  //    * Get notification by ID
  //    */
  //   async getNotification(id: string) {
  //     return await this.notificationRepository.findById(id);
  //   }
  //   /**
  //    * Get notification results
  //    */
  //   async getNotificationResults(notificationId: string) {
  //     return await this.notificationResultRepository.findByNotificationId(notificationId);
  //   }
  //   /**
  //    * List notifications with pagination and filters
  //    */
  //   async listNotifications(options: ListNotificationsOptions) {
  //     const { skip, take, status, channel } = options;
  //     const filters: any = {};
  //     if (status) {
  //       filters.status = status;
  //     }
  //     if (channel) {
  //       filters.channels = { has: channel };
  //     }
  //     const [notifications, total] = await Promise.all([
  //       this.notificationRepository.findMany({
  //         where: filters,
  //         skip,
  //         take,
  //         orderBy: { createdAt: 'desc' },
  //       }),
  //       this.notificationRepository.count({ where: filters }),
  //     ]);
  //     return { notifications, total };
  //   }
  //   /**
  //    * Retry a failed notification
  //    */
  //   async retryNotification(id: string) {
  //     const notification = await this.notificationRepository.findById(id);
  //     if (!notification) {
  //       throw new NotFoundException(`Notification with ID ${id} not found`);
  //     }
  //     if (
  //       notification.status !== NotificationStatus.FAILED &&
  //       notification.status !== NotificationStatus.PARTIAL
  //     ) {
  //       throw new BadRequestException(
  //         `Notification with status ${notification.status} cannot be retried`
  //       );
  //     }
  //     // Update status to queued
  //     await this.notificationRepository.updateStatus(id, NotificationStatus.QUEUED);
  //     // Create retry queue message
  //     const queueMessage: NotificationQueueMessage = {
  //       id: notification.id,
  //       notification: {
  //         id: notification.id,
  //         templateId: notification.templateId,
  //         payload: notification.payload,
  //         recipients: notification.recipients,
  //         channels: notification.channels,
  //         priority: notification.priority,
  //         scheduledAt: notification.scheduledAt,
  //         metadata: notification.metadata,
  //         createdBy: notification.createdBy,
  //       },
  //       retryCount: 0, // Reset retry count for manual retry
  //       maxRetries: 3,
  //       createdAt: new Date(),
  //     };
  //     // Route to appropriate queues
  //     await this.queueRouterService.routeNotification(queueMessage);
  //     this.logger.log(`Notification ${id} queued for retry`);
  //   }
  //   /**
  //    * Get notification statistics
  //    */
  //   async getNotificationStats() {
  //     const [total, queued, sent, failed, partial] = await Promise.all([
  //       this.notificationRepository.count(),
  //       this.notificationRepository.count({ where: { status: NotificationStatus.QUEUED } }),
  //       this.notificationRepository.count({ where: { status: NotificationStatus.SENT } }),
  //       this.notificationRepository.count({ where: { status: NotificationStatus.FAILED } }),
  //       this.notificationRepository.count({ where: { status: NotificationStatus.PARTIAL } }),
  //     ]);
  //     return {
  //       total,
  //       queued,
  //       sent,
  //       failed,
  //       partial,
  //     };
  //   }
  //   /**
  //    * Get channel statistics
  //    */
  //   async getChannelStats() {
  //     // This would require aggregation queries - implement based on your needs
  //     const channels = Object.values(NotificationChannel);
  //     const stats = {};
  //     for (const channel of channels) {
  //       const count = await this.notificationRepository.count({
  //         where: {
  //           channels: { has: channel },
  //         },
  //       });
  //       stats[channel] = count;
  //     }
  //     return stats;
  //   }
}
