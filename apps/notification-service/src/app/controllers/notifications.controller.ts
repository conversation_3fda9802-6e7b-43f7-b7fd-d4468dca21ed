import {
  Controller,
  Post,
  Get,
  Body,
  Param,
  Query,
  HttpStatus,
  HttpException,
  Logger,
  ValidationPipe,
  UsePipes,
  Inject,
  type OnModuleInit,
  type OnModuleDestroy,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody, ApiParam, ApiQuery } from '@nestjs/swagger';
import { NotificationService } from '../services/notification.service';
import {
  CreateNotificationDto,
  NotificationResponseDto,
  NotificationListResponseDto,
} from '../dto/notification.dto';
import { FCM_QUEUE, NotificationStatus } from '@libs';
import type { ClientProxy } from '@nestjs/microservices';

@ApiTags('Notifications')
@Controller('notifications')
export class NotificationsController implements OnModuleInit {
  constructor(@Inject('FCM_QUEUE') private readonly fcmQueue: ClientProxy) {}
  //
  onModuleInit() {
    this.fcmQueue.connect();
  }
  @Get('test')
  async test() {
    await this.fcmQueue
      .emit(
        'notification.fcm.normal',
        JSON.stringify({
          id: '123',
          notification: { title: 'Hello World' },
        })
      )
      .toPromise();

    await this.fcmQueue
      .emit(
        'notification.fcm.high',
        JSON.stringify({
          id: '123',
          notification: { title: 'Hello World' },
        })
      )
      .toPromise();
    return {
      message: 'Hello World',
    };
  }
  //   private readonly logger = new Logger(NotificationsController.name);
  //   constructor(private readonly notificationService: NotificationService) {}
  //   @Post()
  //   @ApiOperation({ summary: 'Send a notification' })
  //   @ApiBody({ type: CreateNotificationDto })
  //   @ApiResponse({
  //     status: 201,
  //     description: 'Notification queued successfully',
  //     type: NotificationResponseDto,
  //   })
  //   @ApiResponse({
  //     status: 400,
  //     description: 'Invalid request data',
  //   })
  //   @ApiResponse({
  //     status: 500,
  //     description: 'Internal server error',
  //   })
  //   @UsePipes(new ValidationPipe({ transform: true }))
  //   async sendNotification(
  //     @Body() createNotificationDto: CreateNotificationDto
  //   ): Promise<NotificationResponseDto> {
  //     try {
  //       this.logger.log(
  //         `Sending notification to ${createNotificationDto.recipients.length} recipients`
  //       );
  //       const notification = await this.notificationService.sendNotification(createNotificationDto);
  //       return {
  //         id: notification.id,
  //         status: notification.status,
  //         message: 'Notification queued successfully',
  //         queuedAt: notification.createdAt,
  //         recipients: notification.recipients.length,
  //         channels: notification.channels,
  //       };
  //     } catch (error) {
  //       this.logger.error(`Failed to send notification: ${error.message}`);
  //       throw new HttpException(
  //         {
  //           status: HttpStatus.INTERNAL_SERVER_ERROR,
  //           error: 'Failed to send notification',
  //           message: error.message,
  //         },
  //         HttpStatus.INTERNAL_SERVER_ERROR
  //       );
  //     }
  //   }
  //   @Get(':id')
  //   @ApiOperation({ summary: 'Get notification status' })
  //   @ApiParam({ name: 'id', description: 'Notification ID' })
  //   @ApiResponse({
  //     status: 200,
  //     description: 'Notification details retrieved successfully',
  //     type: NotificationResponseDto,
  //   })
  //   @ApiResponse({
  //     status: 404,
  //     description: 'Notification not found',
  //   })
  //   async getNotification(@Param('id') id: string): Promise<NotificationResponseDto> {
  //     try {
  //       const notification = await this.notificationService.getNotification(id);
  //       if (!notification) {
  //         throw new HttpException('Notification not found', HttpStatus.NOT_FOUND);
  //       }
  //       const results = await this.notificationService.getNotificationResults(id);
  //       return {
  //         id: notification.id,
  //         status: notification.status,
  //         message: 'Notification details retrieved successfully',
  //         queuedAt: notification.createdAt,
  //         sentAt: notification.sentAt,
  //         recipients: notification.recipients.length,
  //         channels: notification.channels,
  //         results: results.map(result => ({
  //           recipientId: result.recipientId,
  //           channel: result.channel,
  //           status: result.status,
  //           sentAt: result.sentAt,
  //           deliveredAt: result.deliveredAt,
  //           error: result.error,
  //         })),
  //       };
  //     } catch (error) {
  //       if (error instanceof HttpException) {
  //         throw error;
  //       }
  //       this.logger.error(`Failed to get notification ${id}: ${error.message}`);
  //       throw new HttpException(
  //         {
  //           status: HttpStatus.INTERNAL_SERVER_ERROR,
  //           error: 'Failed to retrieve notification',
  //           message: error.message,
  //         },
  //         HttpStatus.INTERNAL_SERVER_ERROR
  //       );
  //     }
  //   }
  //   @Get()
  //   @ApiOperation({ summary: 'List notifications' })
  //   @ApiQuery({ name: 'page', required: false, description: 'Page number (default: 1)' })
  //   @ApiQuery({ name: 'limit', required: false, description: 'Items per page (default: 10)' })
  //   @ApiQuery({
  //     name: 'status',
  //     required: false,
  //     enum: NotificationStatus,
  //     description: 'Filter by status',
  //   })
  //   @ApiQuery({ name: 'channel', required: false, description: 'Filter by channel' })
  //   @ApiResponse({
  //     status: 200,
  //     description: 'Notifications retrieved successfully',
  //     type: NotificationListResponseDto,
  //   })
  //   async listNotifications(
  //     @Query('page') page = 1,
  //     @Query('limit') limit = 10,
  //     @Query('status') status?: NotificationStatus,
  //     @Query('channel') channel?: string
  //   ): Promise<NotificationListResponseDto> {
  //     try {
  //       const skip = (page - 1) * limit;
  //       const { notifications, total } = await this.notificationService.listNotifications({
  //         skip,
  //         take: limit,
  //         status,
  //         channel,
  //       });
  //       return {
  //         data: notifications.map(notification => ({
  //           id: notification.id,
  //           status: notification.status,
  //           recipients: notification.recipients.length,
  //           channels: notification.channels,
  //           createdAt: notification.createdAt,
  //           sentAt: notification.sentAt,
  //         })),
  //         pagination: {
  //           page,
  //           limit,
  //           total,
  //           pages: Math.ceil(total / limit),
  //         },
  //       };
  //     } catch (error) {
  //       this.logger.error(`Failed to list notifications: ${error.message}`);
  //       throw new HttpException(
  //         {
  //           status: HttpStatus.INTERNAL_SERVER_ERROR,
  //           error: 'Failed to list notifications',
  //           message: error.message,
  //         },
  //         HttpStatus.INTERNAL_SERVER_ERROR
  //       );
  //     }
  //   }
  //   @Post(':id/retry')
  //   @ApiOperation({ summary: 'Retry a failed notification' })
  //   @ApiParam({ name: 'id', description: 'Notification ID' })
  //   @ApiResponse({
  //     status: 200,
  //     description: 'Notification retry queued successfully',
  //   })
  //   @ApiResponse({
  //     status: 404,
  //     description: 'Notification not found',
  //   })
  //   @ApiResponse({
  //     status: 400,
  //     description: 'Notification cannot be retried',
  //   })
  //   async retryNotification(@Param('id') id: string): Promise<{ message: string }> {
  //     try {
  //       await this.notificationService.retryNotification(id);
  //       return {
  //         message: 'Notification retry queued successfully',
  //       };
  //     } catch (error) {
  //       if (error.message.includes('not found')) {
  //         throw new HttpException('Notification not found', HttpStatus.NOT_FOUND);
  //       }
  //       if (error.message.includes('cannot be retried')) {
  //         throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
  //       }
  //       this.logger.error(`Failed to retry notification ${id}: ${error.message}`);
  //       throw new HttpException(
  //         {
  //           status: HttpStatus.INTERNAL_SERVER_ERROR,
  //           error: 'Failed to retry notification',
  //           message: error.message,
  //         },
  //         HttpStatus.INTERNAL_SERVER_ERROR
  //       );
  //     }
  //   }
}
