import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Twilio } from 'twilio';
import {
  NotificationChannel,
  NotificationStatus,
  type NotificationRecipient,
  type NotificationResult,
} from '../../../shared';
import { NotificationProvider } from '../notification.provider';
import { SmsMessage } from '../../messages/sms-message';

@Injectable()
export class SmsProvider extends NotificationProvider<SmsMessage> {
  readonly channel = NotificationChannel.Sms;
  private client: Twilio;
  private fromNumber: string;

  constructor(private configService: ConfigService) {
    super();
    this.initializeTwilio();
  }

  private initializeTwilio(): void {
    try {
      const accountSid = this.configService.get<string>('TWILIO_ACCOUNT_SID');
      const authToken = this.configService.get<string>('TWILIO_AUTH_TOKEN');
      this.fromNumber = this.configService.get<string>('TWILIO_FROM_NUMBER') ?? '';

      if (!accountSid || !authToken || !this.fromNumber) {
        this.logger.warn('Twilio configuration is incomplete. SMS provider will be disabled.');
        return;
      }

      this.client = new Twilio(accountSid, authToken);
      this.logger.log('Twilio client initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize Twilio client', error);
    }
  }

  async send(payload: SmsMessage): Promise<NotificationResult> {
    const recipient = payload.getRecipient();
    try {
      if (!this.client) {
        throw new Error('Twilio client not initialized');
      }

      if (!this.validatePayload(payload) || !this.validateRecipient(recipient)) {
        throw new Error('Invalid payload or recipient');
      }

      // Construct SMS message
      let messageBody = `${payload.getSubject()}\n\n${payload.getContent()}`;

      // SMS has character limits, truncate if necessary
      if (messageBody.length > 1600) {
        messageBody = messageBody.substring(0, 1597) + '...';
      }

      const message = await this.client.messages.create({
        body: messageBody,
        from: this.fromNumber,
        to: recipient.address,
      });

      this.logger.log(`SMS notification sent successfully: ${message.sid}`);

      return this.createResult(recipient.id, NotificationStatus.Sent, {
        messageSid: message.sid,
        phoneNumber: recipient.address,
        status: message.status,
        direction: message.direction,
      });
    } catch (error) {
      return this.handleError(error, recipient.id);
    }
  }

  validateRecipient(recipient: NotificationRecipient): boolean {
    // Basic phone number validation (E.164 format)
    const phoneRegex = /^\+[1-9]\d{1,14}$/;
    return (
      recipient.channel === NotificationChannel.Sms &&
      !!recipient.address &&
      phoneRegex.test(recipient.address)
    );
  }

  isConfigured(): boolean {
    return !!this.client;
  }

  async isHealthy(): Promise<boolean> {
    try {
      if (!this.client) {
        return false;
      }

      // Try to fetch account info to verify connection
      await this.client.api.accounts(this.client.accountSid).fetch();
      return true;
    } catch (error) {
      this.logger.error('SMS health check failed', error);
      return false;
    }
  }

  async getMessageStatus(messageSid: string): Promise<string> {
    try {
      if (!this.client) {
        throw new Error('Twilio client not initialized');
      }

      const message = await this.client.messages(messageSid).fetch();
      return message.status;
    } catch (error) {
      this.logger.error(`Failed to get message status for ${messageSid}`, error);
      throw error;
    }
  }
}
