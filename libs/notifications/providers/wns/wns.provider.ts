import { Injectable } from '@nestjs/common';
import {
  NotificationChannel,
  type NotificationRecipient,
  type NotificationResult,
} from '../../../shared';
import { WnsMessage } from '../../messages/wsn-message';
import { NotificationProvider } from '../notification.provider';

@Injectable()
export class WnsProvider extends NotificationProvider<WnsMessage> {
  send(payload: WnsMessage): Promise<NotificationResult> {
    throw new Error('Method not implemented.');
  }
  validateRecipient(recipient: NotificationRecipient): boolean {
    throw new Error('Method not implemented.');
  }
  isHealthy(): Promise<boolean> {
    throw new Error('Method not implemented.');
  }
  readonly channel = NotificationChannel.Wns;
}
