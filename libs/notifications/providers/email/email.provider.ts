import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as nodemailer from 'nodemailer';
import type {
  NotificationRecipient,
  NotificationResult,
} from '../../../shared/interfaces/notification.interface';
import { NotificationProvider } from '../notification.provider';
import { NotificationChannel, NotificationStatus } from '../../../shared';
import { EmailMessage } from '../../messages/email-message';

@Injectable()
export class EmailProvider extends NotificationProvider<EmailMessage> {
  readonly channel = NotificationChannel.Email;
  private transporter: nodemailer.Transporter;

  constructor(private configService: ConfigService) {
    super();
    this.initializeTransporter();
  }

  private initializeTransporter(): void {
    try {
      const host = this.configService.get<string>('EMAIL_HOST');
      const port = this.configService.get<number>('EMAIL_PORT');
      const secure = this.configService.get<boolean>('EMAIL_SECURE');
      const user = this.configService.get<string>('EMAIL_USER');
      const pass = this.configService.get<string>('EMAIL_PASS');

      if (!host || !port || !user || !pass) {
        this.logger.warn('Email configuration is incomplete. Email provider will be disabled.');
        return;
      }

      this.transporter = nodemailer.createTransport({
        host,
        port,
        secure,
        auth: {
          user,
          pass,
        },
        pool: true,
        maxConnections: 5,
        maxMessages: 100,
        rateDelta: 1000,
        rateLimit: 5,
      });

      this.logger.log('Email transporter initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize email transporter', error);
    }
  }

  async send(message: EmailMessage): Promise<NotificationResult> {
    const recipient = message.getRecipient();
    try {
      if (!this.transporter) {
        throw new Error('Email transporter not initialized');
      }

      const fromEmail = this.configService.get<string>('EMAIL_FROM');

      const mailOptions: nodemailer.SendMailOptions = {
        from: fromEmail,
        to: recipient.address,
        subject: message.getSubject(),
        html: message.getContent(),
      };

      const info = await this.transporter.sendMail(mailOptions);

      this.logger.log(`Email notification sent successfully: ${info.messageId}`);

      return this.createResult(recipient.id, NotificationStatus.Sent, {
        messageId: info.messageId,
        emailAddress: recipient.address,
        response: info.response,
      });
    } catch (error) {
      return this.handleError(error, recipient.id);
    }
  }

  validateRecipient(recipient: NotificationRecipient): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return (
      recipient.channel === NotificationChannel.Email &&
      !!recipient.address &&
      emailRegex.test(recipient.address)
    );
  }

  isConfigured(): boolean {
    return !!this.transporter;
  }

  async testConnection(): Promise<boolean> {
    return await this.isHealthy();
  }

  async isHealthy(): Promise<boolean> {
    try {
      if (!this.transporter) {
        return false;
      }

      await this.transporter.verify();
      return true;
    } catch (error) {
      this.logger.error('Email health check failed', error);
      return false;
    }
  }

  async shutdown(): Promise<void> {
    if (this.transporter) {
      this.transporter.close();
      this.logger.log('Email transporter closed');
    }
  }
}
