import { Logger, type INestApplication } from '@nestjs/common';
import { Transport, type MicroserviceOptions, type RmqOptions } from '@nestjs/microservices';
import { ConfigService } from '@nestjs/config';

type RbMQOptions = RmqOptions['options'];

export async function subscribeQueue(app: INestApplication, options: RbMQOptions) {
  const configService = app.get(ConfigService);
  const rabbitmqUrl = configService.get<string>('RABBITMQ_URL', 'amqp://localhost:5672');
  const priorities = configService
    .get<string>('NOTIFICATION_PRIORITIES', 'normal')
    .split(',')
    .map(x => x.toLowerCase());

  const logger = new Logger('Notification');

  priorities.forEach(priority => {
    const { routingKey, queue, ...restOptions } = options;
    app.connectMicroservice<MicroserviceOptions>({
      transport: Transport.RMQ,
      options: {
        urls: options?.urls ? options?.urls : [rabbitmqUrl],
        exchange: options?.exchange ?? 'notifications',
        exchangeType: 'topic',
        routingKey: `${routingKey}.${priority}`,
        queue: `${queue}.${priority}`,
        queueOptions: {
          durable: true,
        },
        persistent: true,
        prefetchCount: configService.get<number>(
          `NOTIFICATION_PREFETCH_${priority.toUpperCase()}`,
          defaultPrefetch(priority)
        ),
        ...restOptions,
      },
    });
  });

  await app.startAllMicroservices();

  logger.log(`📡 Connected to RabbitMQ: ${rabbitmqUrl}`);
}

function defaultPrefetch(priority: string) {
  return priority === 'critical' || priority === 'high' ? 100 : priority === 'normal' ? 40 : 10;
}
