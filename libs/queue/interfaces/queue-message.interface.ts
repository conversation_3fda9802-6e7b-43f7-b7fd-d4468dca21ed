import {
  NotificationRequest,
  NotificationResult,
} from '../../shared/interfaces/notification.interface';

export interface NotificationQueueMessage {
  id: string;
  notification: NotificationRequest;
  retryCount?: number;
  maxRetries?: number;
  createdAt: Date;
  scheduledAt?: Date;
}

export interface NotificationResultQueueMessage {
  id: string;
  notificationId: string;
  result: NotificationResult;
  createdAt: Date;
}

export interface NotificationRetryQueueMessage {
  id: string;
  originalMessage: NotificationQueueMessage;
  retryCount: number;
  lastError: string;
  nextRetryAt: Date;
  createdAt: Date;
}

export enum QueueEvents {
  NOTIFICATION_CREATED = 'notification.created',
  NOTIFICATION_SENT = 'notification.sent',
  NOTIFICATION_DELIVERED = 'notification.delivered',
  NOTIFICATION_FAILED = 'notification.failed',
  NOTIFICATION_RETRY = 'notification.retry',
  NOTIFICATION_EXPIRED = 'notification.expired',
}
